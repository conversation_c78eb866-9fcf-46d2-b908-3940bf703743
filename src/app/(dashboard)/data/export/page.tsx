'use client'

import { useState, useEffect } from 'react'
import { Card, Form, Select, DatePicker, Button, Space, message, Table, Checkbox } from 'antd'
import { DownloadOutlined, FileExcelOutlined, FilePdfOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'

const { RangePicker } = DatePicker
const { Option } = Select

interface FormConfig {
  id: string
  formId: string
  formName: string
  isActive: boolean
  fieldCount: number
  webhookUrl: string
  createdAt: string
  updatedAt: string
}

interface ExportRecord {
  id: string
  formName: string
  exportType: string
  dateRange: string
  recordCount: number
  fileSize: string
  createdAt: string
  downloadUrl: string
  status: 'processing' | 'completed' | 'failed'
}

export default function DataExportPage() {
  const [forms, setForms] = useState<FormConfig[]>([])
  const [exportHistory, setExportHistory] = useState<ExportRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [historyLoading, setHistoryLoading] = useState(true)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchForms()
    fetchExportHistory()
  }, [])

  const fetchForms = async () => {
    try {
      const response = await fetch('/api/forms')
      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data && Array.isArray(result.data.forms)) {
          setForms(result.data.forms)
        } else {
          console.error('API返回数据格式错误:', result)
          setForms([]) // 确保设置为空数组而不是undefined
          message.error('获取表单列表失败：数据格式错误')
        }
      } else {
        console.error('API请求失败:', response.status, response.statusText)
        setForms([]) // 确保设置为空数组而不是undefined
        message.error('获取表单列表失败')
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      setForms([]) // 确保设置为空数组而不是undefined
      message.error('获取表单列表失败')
    }
  }

  const fetchExportHistory = async () => {
    try {
      setHistoryLoading(true)
      // TODO: 实现导出历史记录 API
      // const response = await fetch('/api/data/export/history')
      // if (response.ok) {
      //   const data = await response.json()
      //   setExportHistory(data)
      // }
      // 模拟数据
      setExportHistory([
        {
          id: '1',
          formName: '肺功能检查表',
          exportType: 'excel',
          dateRange: '2024-01-01 ~ 2024-01-31',
          recordCount: 150,
          fileSize: '2.3MB',
          createdAt: '2024-01-31 14:30:00',
          downloadUrl: '/api/download/export-1.xlsx',
          status: 'completed'
        }
      ])
    } catch (error) {
      console.error('获取导出历史失败:', error)
      message.error('获取导出历史失败')
    } finally {
      setHistoryLoading(false)
    }
  }

  const handleExport = async (values: any) => {
    try {
      setLoading(true)
      const { formId, dateRange, exportType, includeFields } = values

      const params = new URLSearchParams({
        formId,
        exportType,
        includeFields: includeFields?.join(',') || '',
      })

      if (dateRange && dateRange.length === 2) {
        params.append('startDate', dateRange[0].format('YYYY-MM-DD'))
        params.append('endDate', dateRange[1].format('YYYY-MM-DD'))
      }

      const response = await fetch(`/api/data/${formId}/export?${params}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `export-${formId}-${dayjs().format('YYYY-MM-DD-HHmm')}.${exportType}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        message.success('导出成功')
        fetchExportHistory()
      } else {
        message.error('导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    } finally {
      setLoading(false)
    }
  }

  const exportColumns: ColumnsType<ExportRecord> = [
    {
      title: '表单名称',
      dataIndex: 'formName',
      key: 'formName',
    },
    {
      title: '导出类型',
      dataIndex: 'exportType',
      key: 'exportType',
      render: (type) => (
        <Space>
          {type === 'excel' ? <FileExcelOutlined /> : <FilePdfOutlined />}
          {type.toUpperCase()}
        </Space>
      ),
    },
    {
      title: '时间范围',
      dataIndex: 'dateRange',
      key: 'dateRange',
    },
    {
      title: '记录数',
      dataIndex: 'recordCount',
      key: 'recordCount',
      render: (count) => count.toLocaleString(),
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
    },
    {
      title: '导出时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          processing: { color: 'blue', text: '处理中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' }
        }
        const config = statusMap[status as keyof typeof statusMap]
        return <span style={{ color: config.color }}>{config.text}</span>
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        record.status === 'completed' && (
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => window.open(record.downloadUrl)}
          >
            下载
          </Button>
        )
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">数据导出</h1>
        <p className="text-gray-600 mt-1">导出表单数据到 Excel 或 CSV 格式</p>
      </div>

      <Card title="导出数据" className="mb-6">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleExport}
          initialValues={{
            exportType: 'excel',
            dateRange: [dayjs().startOf('month'), dayjs().endOf('month')]
          }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Form.Item
              name="formId"
              label="选择表单"
              rules={[{ required: true, message: '请选择要导出的表单' }]}
            >
              <Select placeholder="请选择表单">
                {Array.isArray(forms) && forms.map(form => (
                  <Option key={form.id} value={form.formId}>
                    {form.formName}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="exportType"
              label="导出格式"
              rules={[{ required: true, message: '请选择导出格式' }]}
            >
              <Select>
                <Option value="excel">Excel (.xlsx)</Option>
                <Option value="csv">CSV (.csv)</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="dateRange"
              label="时间范围"
            >
              <RangePicker
                format="YYYY-MM-DD"
                placeholder={['开始日期', '结束日期']}
                className="w-full"
              />
            </Form.Item>

            <Form.Item
              name="includeFields"
              label="包含字段"
            >
              <Checkbox.Group>
                <div className="space-y-2">
                  <Checkbox value="all">所有字段</Checkbox>
                  <Checkbox value="basic">基础信息</Checkbox>
                  <Checkbox value="timestamp">时间戳</Checkbox>
                </div>
              </Checkbox.Group>
            </Form.Item>
          </div>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<DownloadOutlined />}
            >
              开始导出
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title="导出历史" className="mb-6">
        <Table
          columns={exportColumns}
          dataSource={exportHistory}
          loading={historyLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条导出记录`,
          }}
        />
      </Card>
    </div>
  )
}